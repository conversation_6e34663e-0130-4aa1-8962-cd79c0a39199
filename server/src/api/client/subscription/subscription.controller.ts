import { Body, Controller, Get, Post, Req, UseGuards } from '@nestjs/common';
import { SubscriptionService } from './subscription.service';
import { JwtAuthGuard } from '@/api/user/guards/auth.guard';

@Controller('client/subscriptions')
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Get()
  async getList() {
    return this.subscriptionService.getList();
  }

  @Post('pay')
  @UseGuards(JwtAuthGuard)
  async paySubscription(@Req() req, @Body() body: any) {
    return await this.subscriptionService.pay(body, req.user)
  }

  @Post('paid')
  async onSubscriptionPaid(@Body() body: any) {
    return await this.subscriptionService.onSubscriptionPaid(body);
  }
}
