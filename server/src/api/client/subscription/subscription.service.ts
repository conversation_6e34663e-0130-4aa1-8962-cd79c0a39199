import { BadRequestException, Injectable } from '@nestjs/common';
import { User } from '@/entity/User';
import { PaymentProviderType } from '@/api/client/donation/create-payment.dto';
import { Subscriptions } from '@/common/subscription/subscription.constants';
import { YookassaService } from '@/api/client/donation/payment-providers/yookassa.service';
import { StripeService } from '@/api/client/donation/payment-providers/stripe.service';
import { IPaymentProvider } from '@/api/client/donation/donation.service';
import { UserSubscriptions } from '@/entity/UserSubscriptions';

@Injectable()
export class SubscriptionService {
  private providers: Map<PaymentProviderType, IPaymentProvider> = new Map();

  constructor(
    private readonly yookassaService: YookassaService,
    private readonly stripeService: StripeService,
  ) {
    this.providers.set(PaymentProviderType.YOOKASSA, this.yookassaService);
    this.providers.set(PaymentProviderType.STRIPE, this.stripeService);
  }

  async getList() {
    return Subscriptions;
  }

  async pay(body: any, user: User) {
    const subscription = Subscriptions[body.type];
    const payment = body.payment;
    const price = body.payment === 'stripe' ? subscription.price.eur : subscription.price.rub;
    const provider = this.providers.get(payment);

    if (!provider) {
      throw new BadRequestException('Выбранный способ оплаты не поддерживается.');
    }

    let currency: string;
    const description = `Оплата подписки ${subscription.name}, пользователь ${user.firstName} ${user.lastName}`;

    if (body.payment === PaymentProviderType.STRIPE) {
      currency = 'EUR';
    } else if (body.payment === PaymentProviderType.YOOKASSA) {
      currency = 'RUB';
    }

    const metadata = {
      module: 'subscriptions',
      value: body.type,
      userId: user.id,
    }

    return provider.createPayment(price, currency, description, metadata);
  }

  async onSubscriptionPaid(body: any)  {
    if(body.event !== 'payment.succeeded' || body.object.metadata?.module !== 'subscriptions') {
      return false;
    }

    const user = await User.findOne({
      where: {
        id: Number(body.object.metadata.userId)
      },
      relations: ['subscriptions'],
    });

    if (!user) {
      return false;
    }

    const newSubscription = new UserSubscriptions();
    newSubscription.type = body.object.metadata?.value;

    await newSubscription.save();

    user.subscriptions.push(newSubscription);

    return await user.save();
  }
}
