import { BaseEntity, Column, CreateDateColumn, Entity, ManyToMany, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Subscriptions, SubscriptionType } from '@/common/subscription/subscription.constants';
import { User } from '@/entity/User';

@Entity()
export class UserSubscriptions extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn()
  createdAt: Date;

  @Column()
  type: SubscriptionType

  @ManyToOne(() => User, (user) => user.subscriptions)
  user: User;
}