<div class="middle_stripe">
  <breadcrumb></breadcrumb>
  @if(profileService.profile) {
  <div class="tab-container profile relative">
    <div class="wrapper_line custom_">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">Намасте{{profileService.profile.firstName || profileService.profile.lastName ? ',' :
          ''}} {{profileService.profile.firstName}} {{profileService.profile.lastName}}</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
    </div>
    <div class="flex relative justify-center tabs_w" #tabs_w>
      <div class="profile-tabs">
        <ng-container *ngFor="let tab of profileTabs">
          <div class="profile-tab" [ngClass]="{'is-active': activeTab === tab }" (click)="selectTab(tab)">
            {{tab}}
          </div>
        </ng-container>
      </div>
    </div>
    <!-- <div class="profile-links mt-5 mb-5 flex justify-center">
        <a class="btn btn-primary" [routerLink]="['/ru/profile/favourites']" href="#">Избранное</a>
        <a class="btn btn-primary" [routerLink]="['/ru/profile/playlist']" href="#">Плейлист</a>
        <a class="btn btn-primary" (click)="$event.preventDefault(); formTab = true" href="#">Анкета</a>
      </div> -->
  </div>
  }
</div>
<div class="line_th"></div>
<div class="middle_stripe">
  @if(profileService.profile) {
  <div class="tab-container profile relative">
    <div class="tab-content" [ngSwitch]="activeTab">
      <div class="flex flex-col items-center w-full" *ngSwitchCase="ProfileTabs.MY_DATA">
        <form [formGroup]="form" class="profile-form flex flex-col relative">
          <div class="avatar flex justify-center items-center" (click)="triggerFileInput()">
            <img class="profile-avatar" *ngIf="form.value.avatar; else emptyAvatar"
              [src]="environment.serverUrl + '/upload/' + form.value.avatar!.name" alt="">
            <ng-template #emptyAvatar>
              <div class="empty-avatar"></div>
            </ng-template>
            <input #fileInput class="hidden" type="file" accept="image/*" (change)="uploadAvatar($event)">
          </div>
          <div class="flex flex-col gap-1">
            <label>Имя</label>
            <div class="field-wrapper">
              <input type="text" formControlName="firstName">
              <span class="sufix">*</span>
            </div>
          </div>
          <div class="flex flex-col gap-1">
            <label>Фамилия</label>
            <div class="field-wrapper">
              <input type="text" formControlName="lastName">
              <span class="sufix">*</span>
            </div>
          </div>
          <div class="flex flex-col gap-1">
            <label>Отчество</label>
            <div class="field-wrapper">
              <input type="text" formControlName="middleName">
            </div>
          </div>
          <div class="flex flex-col gap-1">
            <label>Духовное имя (если есть)</label>
            <div class="field-wrapper">
              <input type="text" formControlName="spiritualName">
            </div>
          </div>
          <div class="flex flex-col gap-1">
            <label>E-mail</label>
            <div class="field-wrapper">
              <input type="text" formControlName="email">
            </div>
          </div>
          <div class="flex flex-col gap-1">
            <label>Телефон</label>
            <div class="field-wrapper">
              <input type="text" formControlName="phone">
            </div>
          </div>
          <div class="flex flex-col gap-1">
            <label>Telegram</label>
            <div class="field-wrapper">
              <input type="text" formControlName="telegram">
            </div>
          </div>
          <!-- <div class="flex flex-col gap-1">
              <label>Аватар</label>
              <img class="profile-avatar" *ngIf="form.value.avatar" [src]="environment.serverUrl + '/upload/' + form.value.avatar!.name" alt="">
              <input type="file" (change)="uploadAvatar($event)">
            </div> -->
          <button type="submit" class="save-btn" (click)="onSubmit()">
            <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
            <div class="save-btn-label">Сохранить</div>
          </button>
        </form>
        <div *ngIf="authService.token()" class="button_img side">
          <a [routerLink]="['/ru/signin']" (click)="logout()" class="exit-button">
            Выход
          </a>
        </div>

      </div>
      <div class="flex w-full" *ngSwitchCase="ProfileTabs.FAVORITES">
        <app-favourites class="grow"></app-favourites>
      </div>
      <div class="flex w-full" *ngSwitchCase="ProfileTabs.PLAYLISTS">
        <app-playlist class="grow"></app-playlist>
      </div>
      <div class="flex flex-col items-center w-full" *ngSwitchCase="ProfileTabs.QUESTIONNAIRES">
        <ProfileForm [isProfile]="true" />
      </div>

      <div class="flex flex-col items-center w-full" *ngSwitchCase="ProfileTabs.SUBSCRIPTIONS">

        <div>
          <div class="subscriptions-active">
            <div>Активные подписки:</div>
            <div class="subscriptions-list">
              <div class="subscription-item" *ngFor="let sub of profileService.profile.subscriptions">
                {{subscriptions[$any(sub).type].name}} до {{activeUntil($any(sub).createdAt)}}
              </div>
            </div>
          </div>

          <form class="payment-form" [formGroup]="subscriptionForm" action="">
            <div class="form-control">
              <div>Выберите подписку</div>
              <select formControlName="type">
                <option [value]="null">Не выбрано</option>
                <option [value]="sub.key" *ngFor="let sub of subscriptionsFiltered | keyvalue">
                  {{$any(sub.value).name}}
                </option>
              </select>
            </div>
            <div class="form-control">
              <div>Платежная система</div>
              <input formControlName="payment" type="radio" value="stripe"> Stripe (Европа)
              <input formControlName="payment" type="radio" value="yookassa"> ЮКасса (СНГ)
            </div>
            <div *ngIf="subscriptionForm.value.type">
              <div class="form-control" *ngIf="subscriptionForm.value.payment === 'stripe'">
                Цена: {{subscriptions[subscriptionForm.value.type].price.eur}} EUR
              </div>
              <div class="form-control" *ngIf="subscriptionForm.value.payment === 'yookassa'">
                Цена: {{subscriptions[subscriptionForm.value.type].price.rub}} RUB
              </div>
            </div>
            <button type="submit" class="save-btn" (click)="paySubscription()">
              <img width="234" height="50" alt="bg" class="btn-backdrop-img" src="assets/images/Button1_1_ .svg">
              <div class="save-btn-label">Оплатить</div>
            </button>
          </form>
        </div>
      </div>
    </div>

  </div>
  }
</div>
