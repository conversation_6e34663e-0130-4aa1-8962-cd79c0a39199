import {FormB<PERSON>er, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {ProfileService} from "@/services/profile.service";
import {Component, inject, ViewChild, ElementRef} from '@angular/core';
import {Router, ActivatedRoute, RouterLink, NavigationEnd} from "@angular/router";
import {CommonModule, NgOptimizedImage} from "@angular/common";
import {filter} from "rxjs/operators";
import { ToasterService } from "@/services/toaster.service";
import {FileService} from "@/services/file.service";
import {environment} from "@/env/environment";
import {ProfileFormComponent} from "@/components/profile-form/profile-form.component";
import { AuthService } from "@/services/auth.service";
import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component";
import { FavouritesComponent } from "./favourites/favourites.component";
import { PlaylistComponent } from "./playlist/playlist.component";
import moment from "moment/moment";

enum ProfileTabs {
  FAVORITES = 'Избранное',
  PLAYLISTS = 'Плейлисты',
  MY_DATA = 'Мои данные',
  QUESTIONNAIRES = 'Анкета',
  SUBSCRIPTIONS = 'Подписки',
}

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    RouterLink,
    CommonModule,
    ProfileFormComponent,
    BreadcrumbComponent,
    NgOptimizedImage,
    FavouritesComponent,
    PlaylistComponent,
    FormsModule
  ],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss'
})
export class ProfileComponent {
  profileService = inject(ProfileService)
  fb = inject(FormBuilder)
  toasterService = inject(ToasterService);
  fileService = inject(FileService);
  router = inject(Router);
  route = inject(ActivatedRoute);
  form: any = this.fb.group({
    id: [null],
    firstName: [null],
    lastName: [null],
    middleName: [null],
    spiritualName: [null],
    email: [null],
    phone: [null],
    telegram: [null],
    avatar: [null]
  })
  statuses: any
  formTab = false
  authService = inject(AuthService);
  profileTabs: ProfileTabs[] = Object.values(ProfileTabs);
  activeTab: ProfileTabs = this.profileTabs[0];
  ProfileTabs = ProfileTabs;
  subscriptions: any = {}

  subscriptionForm = this.fb.group({
    type: [null, Validators.required],
    payment: ['stripe', Validators.required],
  })

  // Маппинг табов к URL путям
  private tabToRouteMap: Record<ProfileTabs, string> = {
    [ProfileTabs.FAVORITES]: 'favorites',
    [ProfileTabs.MY_DATA]: 'my-data',
    [ProfileTabs.PLAYLISTS]: 'playlists',
    [ProfileTabs.QUESTIONNAIRES]: 'questionnaires',
    [ProfileTabs.SUBSCRIPTIONS]: 'subscriptions',
  };

  private routeToTabMap: Record<string, ProfileTabs> = {
    'favorites': ProfileTabs.FAVORITES,
    'my-data': ProfileTabs.MY_DATA,
    'playlists': ProfileTabs.PLAYLISTS,
    'questionnaires': ProfileTabs.QUESTIONNAIRES,
    'subscriptions': ProfileTabs.SUBSCRIPTIONS,
  };

  @ViewChild('fileInput') fileInput!: ElementRef;
  @ViewChild('tabs_w') tabs_w!: ElementRef;

  ngOnInit() {
    this.init();

    // Подписываемся на изменения роутера для определения активного таба
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        const url = this.router.url;
        if (url.includes('/profile/favorites')) {
          this.activeTab = ProfileTabs.FAVORITES;
        } else if (url.includes('/profile/my-data')) {
          this.activeTab = ProfileTabs.MY_DATA;
        } else if (url.includes('/profile/playlists')) {
          this.activeTab = ProfileTabs.PLAYLISTS;
        } else if (url.includes('/profile/questionnaires')) {
          this.activeTab = ProfileTabs.QUESTIONNAIRES;
        } else if (url.includes('/profile/subscriptions')) {
          this.activeTab = ProfileTabs.SUBSCRIPTIONS;
        }

        this.scrollToActiveTab();
      });

    // Инициализируем активный таб при загрузке
    const currentUrl = this.router.url;
    if (currentUrl.includes('/profile/favorites')) {
      this.activeTab = ProfileTabs.FAVORITES;
    } else if (currentUrl.includes('/profile/my-data')) {
      this.activeTab = ProfileTabs.MY_DATA;
    } else if (currentUrl.includes('/profile/playlists')) {
      this.activeTab = ProfileTabs.PLAYLISTS;
    } else if (currentUrl.includes('/profile/questionnaires')) {
      this.activeTab = ProfileTabs.QUESTIONNAIRES;
    } else if (currentUrl.includes('/profile/subscriptions')) {
      this.activeTab = ProfileTabs.SUBSCRIPTIONS;
    }
  }

  init() {
    this.form = this.fb.group({
      id: [null],
      firstName: [null],
      lastName: [null],
      middleName: [null],
      spiritualName: [null],
      email: [null],
      phone: [null],
      telegram: [null],
      avatar: [null]
    });

    this.profileService.getStatuses().subscribe(res => this.statuses = res)
    if(this.profileService?.profile) {
      this.form.patchValue(this.profileService.profile as any)
    } else {
      this.profileService.getProfile().subscribe(res => {
        this.form.patchValue(res)
      })
    }

    this.profileService.getSubscriptions().subscribe(res => this.subscriptions = res)
  }

  uploadAvatar(e: Event) {
    const files = (e.target as HTMLInputElement).files!
    this.fileService.upload(files, 'avatar').subscribe((res: any) => {
      this.form.patchValue({ avatar: res[0] });
    })
  }

  onSubmit() {
    this.profileService.update(this.form).subscribe({
      next: () => {
        this.profileService.getProfile().subscribe(()=>{
          this.init();
        });
        this.toasterService.showToast('Профиль успешно обновлен!', 'success', 'bottom-middle', 3000);
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
    return false
  }

  logout() {
    this.authService.logout();
  }

  selectTab(tab: ProfileTabs): void {
    this.navigateToTab(tab);
  }

  private navigateToTab(tab: ProfileTabs): void {
    const route = this.tabToRouteMap[tab];
    this.router.navigate(['/ru/profile', route]);
  }

  private scrollToActiveTab(): void {
    setTimeout(() => {
      if (this.tabs_w?.nativeElement) {
        const tabsContainer = this.tabs_w.nativeElement;
        const activeTabElement = tabsContainer.querySelector('.is-active');

        if (activeTabElement) {
          const containerWidth = tabsContainer.offsetWidth;
          const tabWidth = activeTabElement.offsetWidth;
          const tabLeft = activeTabElement.offsetLeft;

          const scrollPosition = tabLeft - (containerWidth / 2) + (tabWidth / 2);

          tabsContainer.scrollTo({
            left: scrollPosition,
            behavior: 'smooth'
          });
        }
      }
    }, 0);
  }

  triggerFileInput(): void {
    if (this.fileInput?.nativeElement)
      this.fileInput.nativeElement.click();
  }

  paySubscription() {
    this.profileService.paySubscription(this.subscriptionForm.value).subscribe((res: any) => {
      location.href = res.paymentUrl;
    })
  }

  activeUntil(date: any) {
    return moment(date).add(1, 'month').format('DD.MM.YYYY HH:mm');
  }

  get subscriptionsFiltered() {
    return Object.fromEntries(Object.keys(this.subscriptions)
      .filter(key => !this.profileService.profile?.subscriptions.some((e: any) => e.type === key))
      .map(key => [key, this.subscriptions[key]]))
  }

  protected readonly environment = environment;
}
