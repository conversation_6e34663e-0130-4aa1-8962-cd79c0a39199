.profile {
  width: 100%;
  // max-width: 900px;
  // margin: 75px auto;
  // padding: 20px;
  // border: 1px solid gray;
  // border-radius: 10px;
  // background-image: linear-gradient(360deg, #f2ead4 4.3%, #d6c28d);
}

.wrapper_line {
  padding: 120px 0 0px 0;
}

.tab-content {
  padding: 60px 0 120px 0;
}

.avatar {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: -240px;

  .empty-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-image: url(assets/images/avatar.svg);
    transition: background-image 200ms ease-in-out;
    cursor: pointer;

    &:hover {
      background-image: url(assets/images/avatar_hover.svg);
    }
  }
}

.tabs_w {
  margin-top: -60px;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  display: none;
  -webkit-appearance: none;
}

input[type="date"]::-webkit-clear-button {
  display: none;
}

input[type="date"] {
  -moz-appearance: textfield;
}

input[type="date"]::-webkit-inner-spin-button {
  display: none;
}

.profile-form {
  gap: 20px;

  .field-wrapper {
    position: relative;

    input {
      width: 450px;
      height: 50px;
      border-radius: 15px;
      outline: none;
      padding: 13px 25px;
      border: 1px solid var(--text-color);
      background: transparent;
      margin: 0 auto;
      font-family: Prata;
      font-weight: 400;
      font-size: 20px;
      line-height: 24px;
      letter-spacing: 0;
      color: var(--font-color1);
    }

    .sufix {
      position: absolute;
      top: 13px;
      right: 13px;
      font-family: Prata;
      font-weight: 400;
      font-size: 20px;
      line-height: 24px;
      letter-spacing: 0;
      color: var(--font-color1);
    }
  }
}


.profile-form label {
  font-family: Prata;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  letter-spacing: 0;
  color: var(--font-color1);
}

.save-btn {
  width: 234px;
  height: 50px;
  padding: 0;
  position: relative;
  margin: 60px auto 35px !important;

  .btn-backdrop-img {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    object-fit: cover;
  }

  .save-btn-label {
    margin: 0 auto;
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 20px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    padding: 14px 25px;
    color: var(--font-color1);
  }
}

.exit-button {
  color: var(--font-color1);
  margin: 0 auto;
  background: url(assets/images/exit_button.svg);
  cursor: pointer;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 20px;
  letter-spacing: 0;
  text-align: center;
  width: 187px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}

a.btn.btn-primary {
  margin: 0 10px !important;
}

.profile-links {
  margin: 15px -10px;
}

.profile-links a {
  text-decoration: underline;
  margin: 0px 10px;
}


.profile-tabs {
  display: flex;
  justify-content: center;
  z-index: 1;
}

.profile-tabs::after {
  content: '';
  background-image: var(--lib-after);
  width: 100%;
  height: 3px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  position: absolute;
  bottom: 0;
  right: 0;
  display: flex;
  z-index: 5;
}

.book_text.show-full-text {
  -webkit-line-clamp: unset !important;
  display: block !important;
}

.profile-tab.is-active {
  background: var(--tab_active);
  color: rgba(255, 255, 255, 0.9529);
  z-index: 5;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.profile-tab:not(.is-active) {
  cursor: pointer;
}

.profile-tab {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 350px;
  height: 61px;
  background: var(--tab_nominal);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  margin-right: -60px;
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 20px;
  color: var(--text-color);
  position: relative;
  bottom: 1px;
  cursor: pointer;
}

.profile-tab:last-child {
  margin-right: 0;
  z-index: unset;

  &.is-active {
    z-index: 5;
  }
}

.profile-tab:nth-child(2) {
  z-index: 2;

  &.is-active {
    z-index: 5;
  }
}

.profile-tab:nth-child(3) {
  z-index: 1;

  &.is-active {
    z-index: 5;
  }
}

@media (max-width: 1250px) {
  .profile-tab {
    width: 220px;
    height: 38px;
    margin-right: -37px;
    font-size: 18px;
    line-height: 18px;
    color: var(--text-color);
    cursor: pointer;
  }

  .tabs_w {
    width: 768px;
    max-width: 100%;
    margin: -38px auto 0;
  }

  .profile-tabs {
    max-width: 100%;
  }

  .profile-tabs::after {
    content: '';
    background-image: var(--lib-after_md);
    width: 100%;
    height: 2px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    position: absolute;
    bottom: 0;
    right: 0;
    display: flex;
  }

  .tab-content {
    padding: 50px 0 120px;
  }

  .avatar {
    width: 120px;
    height: 120px;
    left: -160px;

    .empty-avatar {
      width: 120px;
      height: 120px;
      background-size: contain;
      background-position: center;
    }
  }

  .profile-form {
    gap: 12px;

    .field-wrapper {
      position: relative;

      input {
        width: 340px;
        height: 40px;
        border-radius: 10px;
        padding: 11px 15px;
        border: 1px solid var(--text-color);
        background: transparent;
        font-size: 18px;
        line-height: 20px;
      }

      .sufix {
        top: 11px;
        right: 8px;
        font-size: 18px;
        line-height: 20px;
      }
    }
  }

  .profile-form label {
    font-size: 14px;
    line-height: 14px;
    letter-spacing: 0;
  }

  .save-btn {
    margin: 58px auto 20px !important;
  }

  .exit-button {
    font-size: 17px;
    line-height: 17px;
  }

}

@media (max-width: 768px) {
  .dec_head-title_ {
    font-size: 40px !important;
    padding: 4px 0 !important;
  }

  .profile-tab {
    bottom: 0;
  }

  .line_th {
    position: relative;
    background-image: var(--lib-after_md);
    width: 100%;
    height: 2px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 112%;
  }

  .profile-tabs::after {
    content: none;
  }

  .dec_head._background {
    margin-top: -137px;
    background-size: 394px;
    height: fit-content;
    padding: 70px 0;

    img {
      max-width: 464px;
    }
  }

  .profile-tab {
    min-width: 220px;
  }

  .tabs_w {
    display: block;
    overflow: auto;
    padding: 0 20px;
  }

  .profile-tabs {
    min-width: calc((220px - 37px)*4);
  }

}

@media (max-width: 720px) {
  .line_th {
    margin-top: -2px;
  }

  .tab-content {
    padding: 40px 0 120px;
  }

  .tabs_w {
    display: block;
    overflow: auto;
    position: sticky;
    margin: -30px -15px 0;
    min-width: calc(100vw - 21px);
    padding: 0 0 0 15px;
  }

  .profile-tabs {
    min-width: calc((135px - 25px)*4);
    width: fit-content;
    margin: 0 auto;
  }

  .avatar {
    position: static;
    margin: 0 auto 18px;
  }


  .profile-tab {
    width: 135px;
    min-width: 135px;
    height: 26px;
    flex: 1 1 0;
    margin-right: -25px;
    font-size: 12px;
    line-height: 12px;
    color: var(--text-color);
    cursor: pointer;
  }

  .profile-form {
    padding: 0 7px;

    .field-wrapper {
      input {
        width: 330px;
        max-width: 100%;
        height: 36px;
        font-size: 14px;
        line-height: 16px;
      }

      .sufix {
        right: 11px;
        font-size: 14px;
        line-height: 16px;
      }
    }
  }

  .profile-form label {
    font-size: 11px;
    line-height: 14px;
    letter-spacing: 0;
  }

  .save-btn {
    margin: 68px auto 20px !important;
  }

  .exit-button {
    font-size: 17px;
    line-height: 17px;
  }
}

@media (max-width: 570px) {
  .dec_head._background {
    img {
      display: none;
    }
  }

  .dec_head-title_ {
    font-size: 32px !important;
    line-height: 32px !important;
  }

  .dec_head._background {
    margin-top: -145px;
    background-size: 339px;
    padding: 60px 0;
  }

  breadcrumb {
    position: sticky;
    z-index: 2;
  }
}

@media (max-width: 500px) {
  .tabs_w {
    margin: -45px -15px 0;
  }
}

@media (max-width: 420px) {
  .tabs_w {
    margin: -220px -15px 0;
  }
}

.payment-form input {
  width: max-content;
}

.form-control {
  padding: 7px 0px;
}

.form-control > div:first-child {
  margin-bottom: 5px;
}

.subscriptions-active {
  margin-bottom: 20px;
  padding: 20px;
}
